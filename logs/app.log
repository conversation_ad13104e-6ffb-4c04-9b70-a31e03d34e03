2025-05-29 14:58:15,456 - test_simple_logging - INFO - [no-request] - setup_logging:116 - Logging configured successfully
2025-05-29 14:58:15,457 - test_simple_logging - INFO - [no-request] - <module>:55 - Flask app logging test - this should appear
2025-05-29 14:58:24,680 - app - INFO - [no-request] - setup_logging:116 - Logging configured successfully
2025-05-29 14:58:24,684 - app - INFO - [3d6698b3] - before_request:153 - Request started
2025-05-29 14:58:24,684 - app - DEBUG - [3d6698b3] - health:448 - Health check requested
2025-05-29 14:58:24,684 - app - INFO - [3d6698b3] - after_request:170 - Request completed
2025-05-29 14:58:34,109 - app - INFO - [no-request] - setup_logging:116 - Logging configured successfully
2025-05-29 14:58:47,371 - app - INFO - [no-request] - setup_logging:116 - Logging configured successfully
2025-05-29 14:58:47,615 - app - INFO - [no-request] - setup_logging:116 - Logging configured successfully
2025-05-29 14:58:59,948 - app - INFO - [44b3c523] - before_request:153 - Request started
2025-05-29 14:58:59,949 - app - INFO - [44b3c523] - after_request:170 - Request completed
2025-05-29 14:59:11,922 - app - INFO - [9e307300] - before_request:153 - Request started
2025-05-29 14:59:12,413 - app - INFO - [9e307300] - after_request:170 - Request completed
2025-05-29 14:59:52,601 - app - INFO - [no-request] - setup_logging:116 - Logging configured successfully
2025-05-29 14:59:52,605 - app - INFO - [ce57628a] - before_request:153 - Request started
2025-05-29 14:59:52,605 - app - DEBUG - [ce57628a] - health:448 - Health check requested
2025-05-29 14:59:52,606 - app - INFO - [ce57628a] - after_request:170 - Request completed
2025-05-29 14:59:52,606 - app - INFO - [48e20215] - before_request:153 - Request started
2025-05-29 14:59:52,606 - app - INFO - [48e20215] - index:178 - Processing index request
2025-05-29 14:59:52,606 - app - DEBUG - [48e20215] - index:189 - Built format URL
2025-05-29 14:59:52,787 - app - DEBUG - [48e20215] - index:216 - Checking custom headers
2025-05-29 14:59:52,970 - app - INFO - [48e20215] - index:244 - Successfully processed content
2025-05-29 14:59:52,972 - app - INFO - [48e20215] - after_request:170 - Request completed
2025-05-29 14:59:52,974 - app - INFO - [8912e1cb] - before_request:153 - Request started
2025-05-29 14:59:52,974 - app - INFO - [8912e1cb] - handle_simple_page:344 - Processing simple page request
2025-05-29 14:59:53,136 - app - ERROR - [8912e1cb] - handle_simple_page:353 - Failed to fetch simple page content
2025-05-29 14:59:53,137 - app - INFO - [8912e1cb] - after_request:170 - Request completed
2025-05-29 14:59:53,138 - app - INFO - [0042b47a] - before_request:153 - Request started
2025-05-29 14:59:53,138 - app - INFO - [0042b47a] - handle_simple_page:344 - Processing simple page request
2025-05-29 14:59:53,333 - app - ERROR - [0042b47a] - handle_simple_page:353 - Failed to fetch simple page content
2025-05-29 14:59:53,333 - app - INFO - [0042b47a] - after_request:170 - Request completed
2025-05-29 14:59:53,333 - app - INFO - [608e6960] - before_request:153 - Request started
2025-05-29 14:59:53,334 - app - INFO - [608e6960] - handle_simple_page:344 - Processing simple page request
2025-05-29 14:59:53,490 - app - ERROR - [608e6960] - handle_simple_page:353 - Failed to fetch simple page content
2025-05-29 14:59:53,491 - app - INFO - [608e6960] - after_request:170 - Request completed
{"timestamp": null, "name": "test_docker_logging", "levelname": "INFO", "request_id": "no-request", "message": "Logging configured successfully", "taskName": null, "log_level": "INFO", "log_file": "logs/app.log", "log_format": "json"}
{"timestamp": null, "name": "test_docker_logging", "levelname": "INFO", "request_id": "no-request", "message": "\ud83d\ude80 Production logging test started", "taskName": null}
{"timestamp": null, "name": "test_docker_logging", "levelname": "INFO", "request_id": "no-request", "message": "\ud83d\udcdd This is an INFO message (should appear)", "taskName": null}
{"timestamp": null, "name": "test_docker_logging", "levelname": "WARNING", "request_id": "no-request", "message": "\u26a0\ufe0f This is a WARNING message (should appear)", "taskName": null}
{"timestamp": null, "name": "test_docker_logging", "levelname": "ERROR", "request_id": "no-request", "message": "\u274c This is an ERROR message (should appear)", "taskName": null}
{"timestamp": null, "name": "test_docker_logging", "levelname": "INFO", "request_id": "no-request", "message": "\ud83d\udcca Production log with context", "taskName": null, "environment": "production", "docker": true, "test_id": "prod_001"}
{"timestamp": null, "name": "test_docker_logging", "levelname": "INFO", "request_id": "4e7c6fff", "message": "Request started", "taskName": null, "method": "GET", "url": "http://localhost/health", "remote_addr": "127.0.0.1", "user_agent": "Werkzeug/3.1.3", "content_length": null}
{"timestamp": null, "name": "test_docker_logging", "levelname": "ERROR", "request_id": "4e7c6fff", "message": "Unhandled exception occurred", "exc_info": "Traceback (most recent call last):\n  File \"/Users/<USER>/GitHub/viding-static/.venv/lib/python3.12/site-packages/flask/app.py\", line 867, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/GitHub/viding-static/.venv/lib/python3.12/site-packages/flask/app.py\", line 841, in dispatch_request\n    self.raise_routing_exception(req)\n  File \"/Users/<USER>/GitHub/viding-static/.venv/lib/python3.12/site-packages/flask/app.py\", line 450, in raise_routing_exception\n    raise request.routing_exception  # type: ignore\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/GitHub/viding-static/.venv/lib/python3.12/site-packages/flask/ctx.py\", line 353, in match_request\n    result = self.url_adapter.match(return_rule=True)  # type: ignore\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/GitHub/viding-static/.venv/lib/python3.12/site-packages/werkzeug/routing/map.py\", line 629, in match\n    raise NotFound() from None\nwerkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.", "taskName": null, "exception_type": "NotFound", "exception_message": "404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again."}
{"timestamp": null, "name": "test_docker_logging", "levelname": "INFO", "request_id": "4e7c6fff", "message": "Request completed", "taskName": null, "status_code": 500, "content_length": 58, "duration_seconds": 0.00397}
{"timestamp": null, "name": "test_docker_logging", "levelname": "INFO", "request_id": "23473db0", "message": "Request started", "taskName": null, "method": "GET", "url": "http://production-test.example.com/", "remote_addr": "127.0.0.1", "user_agent": "Werkzeug/3.1.3", "content_length": null}
{"timestamp": null, "name": "test_docker_logging", "levelname": "ERROR", "request_id": "23473db0", "message": "Unhandled exception occurred", "exc_info": "Traceback (most recent call last):\n  File \"/Users/<USER>/GitHub/viding-static/.venv/lib/python3.12/site-packages/flask/app.py\", line 867, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/GitHub/viding-static/.venv/lib/python3.12/site-packages/flask/app.py\", line 841, in dispatch_request\n    self.raise_routing_exception(req)\n  File \"/Users/<USER>/GitHub/viding-static/.venv/lib/python3.12/site-packages/flask/app.py\", line 450, in raise_routing_exception\n    raise request.routing_exception  # type: ignore\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/GitHub/viding-static/.venv/lib/python3.12/site-packages/flask/ctx.py\", line 353, in match_request\n    result = self.url_adapter.match(return_rule=True)  # type: ignore\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/GitHub/viding-static/.venv/lib/python3.12/site-packages/werkzeug/routing/map.py\", line 629, in match\n    raise NotFound() from None\nwerkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.", "taskName": null, "exception_type": "NotFound", "exception_message": "404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again."}
{"timestamp": null, "name": "test_docker_logging", "levelname": "INFO", "request_id": "23473db0", "message": "Request completed", "taskName": null, "status_code": 500, "content_length": 58, "duration_seconds": 0.003541}
